{"data": {"ruleId": "WCAG-062", "ruleName": "Reading Level", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.6.2", "level": "AAA", "status": "partial", "score": 16, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Reading level analysis", "value": "Overall reading level: Graduate (17th grade and above) (Grade 66.7)", "elementCount": 1, "affectedSelectors": ["Overall", "reading", "level", "Graduate", "th", "grade", "and", "above", "Grade"], "severity": "error", "metadata": {"scanDuration": 302, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-12T18:16:49.220Z"}}}, {"type": "text", "description": "Complex text block (Grade 68.2)", "value": "\"Skip to content\n\t\t\n\t\n\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\t\t\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\n\t\t\t\t\t\n\t\tContact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Sch...\"", "selector": "#wrapper", "severity": "error", "metadata": {"scanDuration": 302, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-12T18:16:49.220Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "<PERSON><PERSON>", "to", "content", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Sch"]}, {"type": "text", "description": "Complex text block (Grade 81.3)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t...\"", "selector": "#main-header", "severity": "error", "metadata": {"scanDuration": 302, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-12T18:16:49.220Z"}}, "elementCount": 1, "affectedSelectors": ["#main-header", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 81.3)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t...\"", "selector": ".site-header-inner-wrap", "severity": "error", "metadata": {"scanDuration": 302, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-12T18:16:49.220Z"}}, "elementCount": 1, "affectedSelectors": [".site-header-inner-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 81.3)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t...\"", "selector": ".site-header-upper-wrap", "severity": "error", "metadata": {"scanDuration": 302, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-12T18:16:49.220Z"}}, "elementCount": 1, "affectedSelectors": [".site-header-upper-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 81.3)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t...\"", "selector": ".site-header-upper-inner-wrap", "severity": "error", "metadata": {"scanDuration": 302, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-12T18:16:49.220Z"}}, "elementCount": 1, "affectedSelectors": [".site-header-upper-inner-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}], "recommendations": ["Simplify sentence structure and use shorter sentences", "Replace complex words with simpler alternatives when possible", "Consider providing a summary or simplified version of complex content", "Use bullet points and lists to break up complex information", "Test content with actual users to ensure comprehension", "CRITICAL: Content may be inaccessible to many users due to complexity", "Consider providing alternative formats (audio, video, infographics)", "Use plain language principles for better accessibility", "Consider your target audience's reading level and education background"], "executionTime": 164, "originalScore": 40, "adjustedScore": 40, "thresholdApplied": 70, "scoringDetails": "Original: 40% → Threshold: 70% → Penalty tier: 30%+ (0.4x) → PARTIAL", "penaltyTier": 0.4, "confidenceAdjustment": 1, "enhancedStatus": "partial"}, "timestamp": 1752344209220, "hash": "5fdfed39dfc23407434fc0ea1186ef50", "accessCount": 1, "lastAccessed": 1752344209220, "size": 5655, "metadata": {"originalKey": "WCAG-062:053b13d2:add92319", "normalizedKey": "wcag-062_053b13d2_add92319", "savedAt": 1752344209220, "version": "1.1", "keyHash": "6b4fd6fbc69df7c45de937c7f6f47a06"}}