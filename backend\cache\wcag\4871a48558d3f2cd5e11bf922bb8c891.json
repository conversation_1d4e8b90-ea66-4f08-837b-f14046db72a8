{"data": {"ruleId": "WCAG-056", "ruleName": "Motion Actuation", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.5.6", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 350, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-12T18:16:32.110Z"}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "recommendations": [], "executionTime": 10, "originalScore": 100, "adjustedScore": 100, "thresholdApplied": 80, "scoringDetails": "Original: 100% → Threshold: 80% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752344192110, "hash": "99394db476d7e5c94fe5562ac12c70b9", "accessCount": 1, "lastAccessed": 1752344192110, "size": 1018, "metadata": {"originalKey": "WCAG-056:053b13d2:add92319", "normalizedKey": "wcag-056_053b13d2_add92319", "savedAt": 1752344192110, "version": "1.1", "keyHash": "2e2617f222f1c5aded9bb5f78617dce7"}}