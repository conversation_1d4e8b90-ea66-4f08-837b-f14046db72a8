{"data": [{"type": "code", "description": "Context change detection: high risk", "value": "Unexpected: 24, User-controlled: 0, Automatic: 0, Warnings: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Unexpected", "User-controlled", "Automatic", "Warnings"], "metadata": {"scanDuration": 262, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 0, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:52.007Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Detected context change types", "value": "new-window", "severity": "info", "elementCount": 1, "affectedSelectors": ["new-window"], "metadata": {"scanDuration": 262, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 1, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:52.007Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "User control validation: Inadequate control mechanisms", "value": "Available controls: none (need at least 2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["Available", "controls", "none", "need", "at", "least"], "metadata": {"scanDuration": 262, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 2, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:52.008Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Unexpected change analysis: Low risk of unexpected changes", "value": "Risk assessment: low, Score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "assessment", "low", "Score"], "metadata": {"scanDuration": 262, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 3, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:52.008Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Accessibility control testing: No context change controls detected", "value": "No context change controls found to test", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "context", "change", "controls", "found", "to", "test"], "metadata": {"scanDuration": 262, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 4, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:52.008Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752344392008, "hash": "75fb04e0f8d7ab160ed7b1673b926a87", "accessCount": 1, "lastAccessed": 1752344392008, "size": 3832, "metadata": {"originalKey": "WCAG-064:WCAG-064", "normalizedKey": "wcag-064_wcag-064", "savedAt": 1752344392008, "version": "1.1", "keyHash": "f45350157f8dc2c45eed744b79ebf19b"}}