{"data": [{"type": "text", "description": "Gesture pattern detection: Low or no accessibility risk", "value": "Risk level: none, Confidence: 90.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "level", "none", "Confidence"], "metadata": {"scanDuration": 32, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-12T14:10:21.835Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Alternative input validation: Sufficient alternatives available", "value": "Keyboard: true, Single-point: true, But<PERSON>: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["Keyboard", "true", "Single-point", "<PERSON><PERSON>"], "metadata": {"scanDuration": 32, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-12T14:10:21.835Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Gesture complexity analysis: Good accessibility rating", "value": "Rating: excellent, Complexity score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Rating", "excellent", "Complexity", "score"], "metadata": {"scanDuration": 32, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-12T14:10:21.835Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752329421835, "hash": "d065d22c9bb5b661c752da6b5fc6e791", "accessCount": 1, "lastAccessed": 1752329421835, "size": 2348, "metadata": {"originalKey": "WCAG-049:WCAG-049", "normalizedKey": "wcag-049_wcag-049", "savedAt": 1752329421836, "version": "1.1", "keyHash": "b985e1a33e6356c889a42f73e24046c0"}}