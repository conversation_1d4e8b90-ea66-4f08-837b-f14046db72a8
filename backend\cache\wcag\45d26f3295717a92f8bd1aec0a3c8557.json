{"data": {"ruleId": "WCAG-057", "ruleName": "Status Messages", "category": "robust", "wcagVersion": "2.2", "successCriterion": "0.5.7", "level": "AA", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "interaction", "description": "Status messages accessibility analysis", "value": "Found 4 status elements and 0 potential status messages", "elementCount": 1, "affectedSelectors": ["Found", "status", "elements", "and", "potential", "messages"], "severity": "info", "metadata": {"scanDuration": 470, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-057", "ruleName": "Status Messages", "timestamp": "2025-07-12T14:13:08.347Z"}}}], "recommendations": ["Add role=\"alert\" and aria-live=\"assertive\" to error messages", "Add role=\"status\" and aria-live=\"polite\" to success and info messages", "Use aria-atomic=\"true\" for messages that should be read completely", "Ensure status messages are programmatically determinable"], "executionTime": 69, "originalScore": 100, "adjustedScore": 100, "thresholdApplied": 85, "scoringDetails": "Original: 100% → Threshold: 85% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752329588347, "hash": "a551f618aaa59732c4b5a8fa4d905740", "accessCount": 1, "lastAccessed": 1752329588347, "size": 1247, "metadata": {"originalKey": "WCAG-057:053b13d2:add92319", "normalizedKey": "wcag-057_053b13d2_add92319", "savedAt": 1752329588348, "version": "1.1", "keyHash": "51044a30d5f4e048dc6369d55d1dc00d"}}