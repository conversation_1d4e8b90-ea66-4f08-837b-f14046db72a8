/**
 * Enhanced Failure Evidence Display Component
 * Displays comprehensive failure evidence and remediation guidance for WCAG checks
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  ChevronDown,
  ChevronRight,
  Code,
  ExternalLink,
  Info,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Target,
  Zap,
  Copy,
  Eye,
  FileText,
  Lightbulb,
  ArrowRight,
  BookOpen,
  ListChecks,
  MapPin,
  Wrench,
  HelpCircle,
  Users,
} from 'lucide-react';
import {
  WcagCheckEnhanced,
  WcagEvidenceEnhanced,
  WcagFixExample,
} from '@/types/wcag';

interface EnhancedFailureEvidenceDisplayProps {
  checks: WcagCheckEnhanced[];
  showFixExamples?: boolean;
  showElementCounts?: boolean;
  showPerformanceMetrics?: boolean;
  groupByCategory?: boolean;
}

interface FailureInstance {
  selector: string;
  element: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  fixSuggestion: string;
  codeExample?: string;
}

const SeverityIcon = ({ severity }: { severity?: string }) => {
  switch (severity) {
    case 'critical':
      return <XCircle className="h-5 w-5 text-red-600" />;
    case 'error':
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    case 'info':
      return <Info className="h-5 w-5 text-blue-500" />;
    default:
      return <CheckCircle className="h-5 w-5 text-green-500" />;
  }
};

const SeverityBadge = ({ severity, count }: { severity?: string; count?: number }) => {
  const getVariant = () => {
    switch (severity) {
      case 'critical':
        return 'destructive';
      case 'error':
        return 'destructive';
      case 'warning':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getColorClass = () => {
    switch (severity) {
      case 'critical':
        return 'bg-red-600 text-white border-red-600';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  return (
    <Badge variant={getVariant()} className={`${getColorClass()} font-medium`}>
      {count ? `${count} element${count !== 1 ? 's' : ''}` : severity}
    </Badge>
  );
};

const CodeBlock = ({ code, language = 'html', showLineNumbers = false }: {
  code: string;
  language?: string;
  showLineNumbers?: boolean;
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const lines = code.split('\n');

  // Simple syntax highlighting for HTML
  const highlightHTML = (htmlCode: string) => {
    return htmlCode
      .replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g,
        '<span class="text-blue-300">$1</span><span class="text-red-300">$2</span><span class="text-yellow-300">$3</span><span class="text-blue-300">$4</span>')
      .replace(/(\w+)=("[^"]*")/g,
        '<span class="text-green-300">$1</span>=<span class="text-orange-300">$2</span>')
      .replace(/(class|id|src|href|alt|aria-\w+|role)=/g,
        '<span class="text-purple-300">$1</span>=');
  };

  return (
    <div className="relative bg-gray-900 text-gray-100 rounded-lg overflow-hidden">
      <div className="flex items-center justify-between bg-gray-800 px-4 py-2 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Code className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-400 capitalize">{language}</span>
          {showLineNumbers && (
            <Badge variant="outline" className="text-xs bg-gray-700 text-gray-300 border-gray-600">
              {lines.length} lines
            </Badge>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="text-gray-400 hover:text-white h-8 px-2"
        >
          <Copy className="h-4 w-4" />
          {copied && <span className="ml-1 text-xs">Copied!</span>}
        </Button>
      </div>

      <div className="p-4 font-mono text-sm overflow-x-auto">
        {showLineNumbers ? (
          <div className="flex">
            <div className="flex flex-col text-gray-500 text-right pr-4 border-r border-gray-700 mr-4 select-none">
              {lines.map((_, index) => (
                <span key={index} className="leading-6">
                  {index + 1}
                </span>
              ))}
            </div>
            <pre className="flex-1 whitespace-pre-wrap break-words">
              <code dangerouslySetInnerHTML={{
                __html: language === 'html' ? highlightHTML(code) : code
              }} />
            </pre>
          </div>
        ) : (
          <pre className="whitespace-pre-wrap break-words">
            <code dangerouslySetInnerHTML={{
              __html: language === 'html' ? highlightHTML(code) : code
            }} />
          </pre>
        )}
      </div>
    </div>
  );
};

const FixExampleDisplay = ({ fixExample }: { fixExample: WcagFixExample }) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-3">
        <Lightbulb className="h-4 w-4 text-yellow-500" />
        <h5 className="font-medium text-gray-900">How to Fix</h5>
      </div>
      
      <p className="text-sm text-gray-700 mb-4">{fixExample.description}</p>
      
      <div className="grid gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <XCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium text-red-700">Before (Incorrect)</span>
          </div>
          <CodeBlock code={fixExample.before} />
        </div>
        
        <div className="flex justify-center">
          <ArrowRight className="h-5 w-5 text-gray-400" />
        </div>
        
        <div>
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium text-green-700">After (Correct)</span>
          </div>
          <CodeBlock code={fixExample.after} />
        </div>
        
        {fixExample.codeExample && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Code className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">Complete Example</span>
            </div>
            <CodeBlock code={fixExample.codeExample} />
          </div>
        )}
      </div>
      
      {fixExample.resources && fixExample.resources.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center gap-2 mb-2">
            <BookOpen className="h-4 w-4 text-purple-500" />
            <span className="text-sm font-medium text-purple-700">Learn More</span>
          </div>
          <div className="space-y-1">
            {fixExample.resources.map((resource, index) => (
              <a
                key={index}
                href={resource}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                <ExternalLink className="h-3 w-3" />
                {resource}
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const StepByStepInstructions = ({ check }: { check: WcagCheckEnhanced }) => {
  const getStepByStepInstructions = (ruleId: string, ruleName: string): string[] => {
    // Generate step-by-step instructions based on the rule
    const instructions: Record<string, string[]> = {
      'WCAG-001': [
        '1. Locate all images on the page without alt attributes',
        '2. Determine if each image is decorative or informative',
        '3. For informative images, add descriptive alt text',
        '4. For decorative images, add alt="" or role="presentation"',
        '5. Test with screen reader to verify alt text is meaningful'
      ],
      'WCAG-002': [
        '1. Identify all video and audio content on the page',
        '2. Check if captions are available for video content',
        '3. Add <track> elements with caption files for videos',
        '4. Ensure captions are synchronized with audio',
        '5. Test captions for accuracy and completeness'
      ],
      'WCAG-003': [
        '1. Use a color contrast analyzer tool',
        '2. Check all text against its background color',
        '3. Ensure contrast ratio meets WCAG standards (4.5:1 for normal text)',
        '4. Adjust colors or add text styling to improve contrast',
        '5. Re-test all modified elements'
      ],
      'WCAG-004': [
        '1. Test all interactive elements with keyboard only',
        '2. Ensure Tab key moves focus in logical order',
        '3. Add tabindex attributes where needed',
        '4. Implement focus indicators for all focusable elements',
        '5. Test with screen reader for proper navigation'
      ],
      'WCAG-005': [
        '1. Review all form inputs for proper labels',
        '2. Add <label> elements or aria-label attributes',
        '3. Associate labels with inputs using for/id attributes',
        '4. Add fieldset and legend for grouped form controls',
        '5. Test form with screen reader for clarity'
      ]
    };

    return instructions[ruleId] || [
      '1. Review the specific WCAG criteria for this check',
      '2. Identify all elements that failed the check',
      '3. Apply the recommended fixes to each element',
      '4. Test the changes with assistive technology',
      '5. Re-run the accessibility scan to verify fixes'
    ];
  };

  const steps = getStepByStepInstructions(check.ruleId, check.ruleName);

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-center gap-2 mb-3">
        <ListChecks className="h-4 w-4 text-blue-600" />
        <h5 className="font-medium text-blue-900">Step-by-Step Fix Instructions</h5>
      </div>
      <ol className="space-y-2">
        {steps.map((step, index) => (
          <li key={index} className="flex items-start gap-3">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-medium">
              {index + 1}
            </span>
            <span className="text-sm text-blue-800">{step.replace(/^\d+\.\s*/, '')}</span>
          </li>
        ))}
      </ol>
    </div>
  );
};

const WcagCriteriaInfo = ({ check }: { check: WcagCheckEnhanced }) => {
  const getCriteriaInfo = (ruleId: string, level: string) => {
    const criteriaMap: Record<string, { criterion: string; description: string; link: string }> = {
      'WCAG-001': {
        criterion: '1.1.1 Non-text Content',
        description: 'All non-text content that is presented to the user has a text alternative that serves the equivalent purpose.',
        link: 'https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html'
      },
      'WCAG-002': {
        criterion: '1.2.2 Captions (Prerecorded)',
        description: 'Captions are provided for all prerecorded audio content in synchronized media.',
        link: 'https://www.w3.org/WAI/WCAG21/Understanding/captions-prerecorded.html'
      },
      'WCAG-003': {
        criterion: '1.4.3 Contrast (Minimum)',
        description: 'The visual presentation of text and images of text has a contrast ratio of at least 4.5:1.',
        link: 'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html'
      },
      'WCAG-004': {
        criterion: '2.1.1 Keyboard',
        description: 'All functionality of the content is operable through a keyboard interface.',
        link: 'https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html'
      },
      'WCAG-005': {
        criterion: '3.3.2 Labels or Instructions',
        description: 'Labels or instructions are provided when content requires user input.',
        link: 'https://www.w3.org/WAI/WCAG21/Understanding/labels-or-instructions.html'
      }
    };

    return criteriaMap[ruleId] || {
      criterion: `WCAG ${level} Criterion`,
      description: 'Specific WCAG success criterion information not available.',
      link: 'https://www.w3.org/WAI/WCAG21/'
    };
  };

  const criteria = getCriteriaInfo(check.ruleId, check.level);

  return (
    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
      <div className="flex items-center gap-2 mb-3">
        <BookOpen className="h-4 w-4 text-purple-600" />
        <h5 className="font-medium text-purple-900">WCAG Success Criterion</h5>
      </div>
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">
            {criteria.criterion}
          </Badge>
          <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">
            Level {check.level}
          </Badge>
        </div>
        <p className="text-sm text-purple-800">{criteria.description}</p>
        <a
          href={criteria.link}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-1 text-sm text-purple-600 hover:text-purple-800 hover:underline"
        >
          <ExternalLink className="h-3 w-3" />
          Learn more about this criterion
        </a>
      </div>
    </div>
  );
};

const RemediationResources = ({ check }: { check: WcagCheckEnhanced }) => {
  const getRemediationResources = (ruleId: string, category: string) => {
    const resources = {
      tools: [] as { name: string; description: string; url: string; type: 'browser' | 'desktop' | 'online' }[],
      documentation: [] as { title: string; description: string; url: string; source: string }[],
      examples: [] as { title: string; description: string; url: string; type: 'codepen' | 'github' | 'demo' }[],
      testing: [] as { method: string; description: string; tools: string[] }[]
    };

    // Add common tools based on rule type
    if (ruleId.includes('001') || category === 'perceivable') {
      resources.tools.push(
        {
          name: 'WAVE Browser Extension',
          description: 'Identifies accessibility errors and warnings in web content',
          url: 'https://wave.webaim.org/extension/',
          type: 'browser'
        },
        {
          name: 'axe DevTools',
          description: 'Accessibility testing toolkit for developers',
          url: 'https://www.deque.com/axe/devtools/',
          type: 'browser'
        }
      );
    }

    if (ruleId.includes('003') || ruleId.includes('contrast')) {
      resources.tools.push(
        {
          name: 'Colour Contrast Analyser',
          description: 'Desktop application for testing color contrast',
          url: 'https://www.tpgi.com/color-contrast-checker/',
          type: 'desktop'
        },
        {
          name: 'WebAIM Contrast Checker',
          description: 'Online tool for checking color contrast ratios',
          url: 'https://webaim.org/resources/contrastchecker/',
          type: 'online'
        }
      );
    }

    // Add documentation resources
    resources.documentation.push(
      {
        title: 'WCAG 2.1 Understanding Documents',
        description: 'Detailed explanations of WCAG success criteria',
        url: 'https://www.w3.org/WAI/WCAG21/Understanding/',
        source: 'W3C'
      },
      {
        title: 'WebAIM Articles',
        description: 'Practical accessibility guidance and tutorials',
        url: 'https://webaim.org/articles/',
        source: 'WebAIM'
      },
      {
        title: 'MDN Accessibility Guide',
        description: 'Developer-focused accessibility documentation',
        url: 'https://developer.mozilla.org/en-US/docs/Web/Accessibility',
        source: 'MDN'
      }
    );

    // Add testing methods
    resources.testing.push(
      {
        method: 'Automated Testing',
        description: 'Use automated tools to identify common accessibility issues',
        tools: ['axe-core', 'Pa11y', 'Lighthouse', 'WAVE']
      },
      {
        method: 'Manual Testing',
        description: 'Test with keyboard navigation and screen readers',
        tools: ['NVDA', 'JAWS', 'VoiceOver', 'TalkBack']
      },
      {
        method: 'User Testing',
        description: 'Test with real users who have disabilities',
        tools: ['UserTesting', 'Fable', 'Access Works']
      }
    );

    return resources;
  };

  const resources = getRemediationResources(check.ruleId, check.category);

  return (
    <div className="space-y-6">
      {/* Tools Section */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <Wrench className="h-4 w-4 text-green-600" />
          <h5 className="font-medium text-green-900">Recommended Tools</h5>
        </div>
        <div className="grid gap-3">
          {resources.tools.map((tool, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-white rounded border border-green-200">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h6 className="font-medium text-green-900">{tool.name}</h6>
                  <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                    {tool.type}
                  </Badge>
                </div>
                <p className="text-sm text-green-800 mb-2">{tool.description}</p>
                <a
                  href={tool.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-sm text-green-600 hover:text-green-800 hover:underline"
                >
                  <ExternalLink className="h-3 w-3" />
                  Get Tool
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Documentation Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <BookOpen className="h-4 w-4 text-blue-600" />
          <h5 className="font-medium text-blue-900">Documentation & Guides</h5>
        </div>
        <div className="space-y-2">
          {resources.documentation.map((doc, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-white rounded border border-blue-200">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h6 className="font-medium text-blue-900">{doc.title}</h6>
                  <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
                    {doc.source}
                  </Badge>
                </div>
                <p className="text-sm text-blue-800 mb-2">{doc.description}</p>
                <a
                  href={doc.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
                >
                  <ExternalLink className="h-3 w-3" />
                  Read Guide
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Testing Methods Section */}
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <Users className="h-4 w-4 text-orange-600" />
          <h5 className="font-medium text-orange-900">Testing Methods</h5>
        </div>
        <div className="space-y-3">
          {resources.testing.map((method, index) => (
            <div key={index} className="p-3 bg-white rounded border border-orange-200">
              <h6 className="font-medium text-orange-900 mb-1">{method.method}</h6>
              <p className="text-sm text-orange-800 mb-2">{method.description}</p>
              <div className="flex flex-wrap gap-1">
                {method.tools.map((tool, toolIndex) => (
                  <Badge key={toolIndex} variant="outline" className="text-xs bg-orange-100 text-orange-700 border-orange-300">
                    {tool}
                  </Badge>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const EvidenceInstanceDisplay = ({ evidence }: { evidence: WcagEvidenceEnhanced }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getEvidenceTypeIcon = (type: string) => {
    switch (type) {
      case 'code':
        return <Code className="h-4 w-4 text-blue-500" />;
      case 'measurement':
        return <Target className="h-4 w-4 text-green-500" />;
      case 'interaction':
        return <MapPin className="h-4 w-4 text-purple-500" />;
      case 'image':
        return <Eye className="h-4 w-4 text-orange-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatEvidenceValue = (evidence: WcagEvidenceEnhanced) => {
    if (evidence.type === 'code') {
      return (
        <div className="mb-2">
          <div className="flex items-center gap-2 mb-2">
            {getEvidenceTypeIcon(evidence.type)}
            <span className="text-sm font-medium text-gray-700">Problematic Element</span>
            {evidence.selector && (
              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                {evidence.selector}
              </Badge>
            )}
          </div>
          <CodeBlock code={evidence.value} language="html" showLineNumbers={evidence.value.split('\n').length > 3} />
        </div>
      );
    } else if (evidence.type === 'measurement') {
      return (
        <div className="mb-2">
          <div className="flex items-center gap-2 mb-2">
            {getEvidenceTypeIcon(evidence.type)}
            <span className="text-sm font-medium text-gray-700">Measurement Result</span>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <code className="text-sm text-green-800 font-mono">{evidence.value}</code>
          </div>
        </div>
      );
    } else if (evidence.type === 'interaction') {
      return (
        <div className="mb-2">
          <div className="flex items-center gap-2 mb-2">
            {getEvidenceTypeIcon(evidence.type)}
            <span className="text-sm font-medium text-gray-700">Interaction Issue</span>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <p className="text-sm text-purple-800">{evidence.value}</p>
          </div>
        </div>
      );
    } else {
      return (
        <div className="mb-2">
          <div className="flex items-center gap-2 mb-2">
            {getEvidenceTypeIcon(evidence.type)}
            <span className="text-sm font-medium text-gray-700 capitalize">{evidence.type} Evidence</span>
          </div>
          <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">{evidence.value}</p>
        </div>
      );
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg mb-3 overflow-hidden">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <SeverityIcon severity={evidence.severity} />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-3 flex-wrap">
                <h4 className="font-medium text-gray-900">{evidence.description}</h4>
                {evidence.elementCount && evidence.elementCount > 0 && (
                  <SeverityBadge severity={evidence.severity} count={evidence.elementCount} />
                )}
                <Badge variant="outline" className="text-xs capitalize">
                  {evidence.type}
                </Badge>
              </div>

              {formatEvidenceValue(evidence)}
            </div>
          </div>

          {(evidence.fixExample || evidence.affectedSelectors || evidence.metadata) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-blue-600 hover:text-blue-800"
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  Hide Details
                </>
              ) : (
                <>
                  <ChevronRight className="h-4 w-4 mr-1" />
                  Show Details
                </>
              )}
            </Button>
          )}
        </div>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent>
            <div className="mt-4 pt-4 border-t border-gray-200 bg-gray-50 -mx-4 px-4 pb-4 space-y-4">
              {evidence.affectedSelectors && evidence.affectedSelectors.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <MapPin className="h-4 w-4 text-blue-500" />
                    <h5 className="text-sm font-medium text-gray-900">All Affected Elements</h5>
                    <Badge variant="outline" className="text-xs">
                      {evidence.affectedSelectors.length} elements
                    </Badge>
                  </div>
                  <div className="grid gap-2">
                    {evidence.affectedSelectors.map((selector, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-white rounded border border-gray-200">
                        <Target className="h-3 w-3 text-gray-400" />
                        <code className="text-xs font-mono text-gray-700 flex-1">{selector}</code>
                        <Badge variant="outline" className="text-xs">
                          Element {index + 1}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {evidence.fixExample && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <Lightbulb className="h-4 w-4 text-yellow-500" />
                    <h5 className="text-sm font-medium text-gray-900">Quick Fix Example</h5>
                  </div>
                  <FixExampleDisplay fixExample={evidence.fixExample} />
                </div>
              )}

              {evidence.metadata?.checkSpecificData && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <Info className="h-4 w-4 text-purple-500" />
                    <h5 className="text-sm font-medium text-gray-900">Technical Details</h5>
                  </div>
                  <div className="bg-white border border-gray-200 rounded-lg p-3">
                    <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono">
                      {JSON.stringify(evidence.metadata.checkSpecificData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {evidence.metadata?.performanceMetrics && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <Zap className="h-4 w-4 text-green-500" />
                    <h5 className="text-sm font-medium text-gray-900">Performance Metrics</h5>
                  </div>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="bg-white p-3 rounded border border-gray-200 text-center">
                      <div className="text-lg font-bold text-green-600">
                        {evidence.metadata.performanceMetrics.domParseTime}ms
                      </div>
                      <div className="text-xs text-gray-600">DOM Parse</div>
                    </div>
                    <div className="bg-white p-3 rounded border border-gray-200 text-center">
                      <div className="text-lg font-bold text-blue-600">
                        {evidence.metadata.performanceMetrics.selectorQueryTime}ms
                      </div>
                      <div className="text-xs text-gray-600">Query Time</div>
                    </div>
                    <div className="bg-white p-3 rounded border border-gray-200 text-center">
                      <div className="text-lg font-bold text-purple-600">
                        {evidence.metadata.performanceMetrics.evaluationTime}ms
                      </div>
                      <div className="text-xs text-gray-600">Evaluation</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
};

const FailedCheckCard = ({
  check,
  showFixExamples = true,
  showElementCounts = true
}: {
  check: WcagCheckEnhanced;
  showFixExamples?: boolean;
  showElementCounts?: boolean;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const failedEvidence = check.evidence?.filter(e => e.severity === 'error' || e.severity === 'critical') || [];
  const totalFailures = failedEvidence.reduce((sum, e) => sum + (e.elementCount || 1), 0);

  return (
    <Card className="mb-6 border-l-4 border-l-red-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <SeverityIcon severity="error" />
            <div>
              <h3 className="font-semibold text-gray-900">{check.ruleName}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{check.ruleId}</Badge>
                <Badge variant="secondary">{check.level}</Badge>
                <Badge variant="outline">{check.category}</Badge>
                {totalFailures > 0 && (
                  <SeverityBadge severity="error" count={totalFailures} />
                )}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-red-600">{check.score}/{check.maxScore}</div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-blue-600 hover:text-blue-800 mt-2"
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  Hide Evidence
                </>
              ) : (
                <>
                  <ChevronRight className="h-4 w-4 mr-1" />
                  Show Evidence
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="pt-0 border-t bg-gray-50">
            <Tabs defaultValue="evidence" className="space-y-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="evidence" className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Evidence
                </TabsTrigger>
                <TabsTrigger value="instructions" className="flex items-center gap-2">
                  <ListChecks className="h-4 w-4" />
                  Fix Instructions
                </TabsTrigger>
                <TabsTrigger value="criteria" className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  WCAG Criteria
                </TabsTrigger>
                <TabsTrigger value="resources" className="flex items-center gap-2">
                  <Wrench className="h-4 w-4" />
                  Resources
                </TabsTrigger>
              </TabsList>

              <TabsContent value="evidence" className="space-y-4">
                {check.evidence && check.evidence.length > 0 ? (
                  check.evidence.map((evidence, index) => (
                    <EvidenceInstanceDisplay key={index} evidence={evidence} />
                  ))
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      No detailed evidence available for this check.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="instructions">
                <StepByStepInstructions check={check} />
              </TabsContent>

              <TabsContent value="criteria">
                <WcagCriteriaInfo check={check} />
              </TabsContent>

              <TabsContent value="resources">
                <RemediationResources check={check} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export const EnhancedFailureEvidenceDisplay: React.FC<EnhancedFailureEvidenceDisplayProps> = ({
  checks,
  showFixExamples = true,
  showElementCounts = true,
  showPerformanceMetrics = false,
  groupByCategory = false,
}) => {
  const failedChecks = checks.filter(check => check.status === 'failed');

  if (failedChecks.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="flex flex-col items-center gap-2">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <h3 className="font-medium text-gray-900">No Failed Checks</h3>
            <p className="text-sm text-gray-600">All accessibility checks passed successfully.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalFailures = failedChecks.reduce((sum, check) => {
    return sum + (check.evidence?.reduce((evidenceSum, e) => evidenceSum + (e.elementCount || 1), 0) || 0);
  }, 0);

  const criticalFailures = failedChecks.filter(check =>
    check.evidence?.some(e => e.severity === 'critical')
  ).length;

  const errorFailures = failedChecks.filter(check =>
    check.evidence?.some(e => e.severity === 'error')
  ).length;

  // Group checks by category if requested
  const groupedChecks = groupByCategory
    ? failedChecks.reduce((groups, check) => {
        const category = check.category || 'other';
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(check);
        return groups;
      }, {} as Record<string, WcagCheckEnhanced[]>)
    : { all: failedChecks };

  const getCategoryTitle = (category: string) => {
    const titles: Record<string, string> = {
      perceivable: 'Perceivable Issues',
      operable: 'Operable Issues',
      understandable: 'Understandable Issues',
      robust: 'Robust Issues',
      all: 'All Failed Checks',
      other: 'Other Issues',
    };
    return titles[category] || category;
  };

  return (
    <div className="space-y-6">
      {/* Summary Header */}
      <Card className="bg-red-50 border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            Detailed Failure Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{failedChecks.length}</div>
              <div className="text-gray-600">Failed Checks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{totalFailures}</div>
              <div className="text-gray-600">Total Issues</div>
            </div>
            {criticalFailures > 0 && (
              <div className="text-center">
                <div className="text-2xl font-bold text-red-700">{criticalFailures}</div>
                <div className="text-gray-600">Critical</div>
              </div>
            )}
            <div className="text-center">
              <div className="text-2xl font-bold text-red-500">{errorFailures}</div>
              <div className="text-gray-600">Errors</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Failed Checks by Category */}
      {Object.entries(groupedChecks).map(([category, categoryChecks]) => (
        <div key={category} className="space-y-4">
          {groupByCategory && (
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              <h2 className="text-xl font-semibold text-gray-900">{getCategoryTitle(category)}</h2>
              <Badge variant="outline">{categoryChecks.length} checks</Badge>
            </div>
          )}

          <div className="space-y-4">
            {categoryChecks.map((check, index) => (
              <FailedCheckCard
                key={`${check.ruleId}-${index}`}
                check={check}
                showFixExamples={showFixExamples}
                showElementCounts={showElementCounts}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default EnhancedFailureEvidenceDisplay;
