{"data": {"totalForms": 0, "totalFields": 0, "accessibleForms": 0, "accessibleFields": 0, "forms": [], "commonIssues": [], "bestPractices": ["Use semantic HTML form elements", "Provide clear instructions and help text", "Use autocomplete attributes for common fields", "Test forms with keyboard navigation", "Validate forms both client-side and server-side"], "overallScore": 100, "criticalIssues": [], "recommendations": ["Ensure all form fields have accessible labels", "Link error messages to fields using aria-describedby", "Add visible focus indicators to all form controls", "Use fieldsets and legends for related form controls", "Implement proper error handling and validation"]}, "timestamp": 1752344115979, "hash": "86fc832afc850d828b5b150daa3e7db5", "accessCount": 1, "lastAccessed": 1752344115979, "size": 650, "metadata": {"originalKey": "https://tigerconnect.com/:form-analysis-{\"analyzeLabels\":true,\"analyzeValidation\":true,\"analyzeGrouping\":true,\"analyzeAutocomplete\":true,\"analyzeErrorHandling\":true,\"analyzeKeyboardAccess\":true,\"includeHiddenFields\":false,\"strictMode\":true}", "normalizedKey": "https_tigerconnect.com_form-analysis-{_analyzelabels_true,_analyzevalidation_true,_analyzegrouping_true,_analyzeautocomplete_true,_analyzeerrorhandlin_0641959c96bd19d3c2daac7f3b2fd39c", "savedAt": 1752344115980, "version": "1.1", "keyHash": "0641959c96bd19d3c2daac7f3b2fd39c"}}