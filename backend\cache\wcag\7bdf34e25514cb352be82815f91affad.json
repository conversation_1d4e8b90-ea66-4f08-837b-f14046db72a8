{"data": {"ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.6.5", "level": "AAA", "status": "passed", "score": 75, "maxScore": 100, "weight": 0.03, "automated": true, "evidence": [{"type": "warning", "description": "Found 21 potential unmarked abbreviations", "value": "Unmarked count: 21, Examples: GTM, IT, ER, ED, EMS", "element": "text content", "elementCount": 1, "affectedSelectors": ["Unmarked", "count", "Examples", "GTM", "IT", "ER", "ED", "EMS"], "metadata": {"scanDuration": 441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-12T14:09:47.339Z"}}}, {"type": "info", "description": "No marked abbreviations or acronyms found", "value": "Marked abbreviations found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Marked", "abbreviations", "found", "false"], "metadata": {"scanDuration": 441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-12T14:09:47.339Z"}}}, {"type": "warning", "description": "Found 55 potential technical terms", "value": "Technical terms count: 55, Examples: ajax_url, siteurl, _prepareurl, plyr_css, ajaxurl, protocol, visibility, community, specificity, isolation", "element": "text content", "elementCount": 1, "affectedSelectors": ["Technical", "terms", "count", "Examples", "ajax_url", "<PERSON><PERSON>l", "<PERSON><PERSON><PERSON>", "plyr_css", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protocol", "visibility", "community", "specificity", "isolation"], "metadata": {"scanDuration": 441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-12T14:09:47.339Z"}}}, {"type": "info", "description": "Foreign language content marked with lang=\"en-US\"", "value": "Language: en-US, Text: if(navigator.userAgent.match(/MSIE|Internet Explorer/i)||navigator.userAgent.match(/Trident\\/7\\..*?r, Is marked: true", "element": "html", "elementCount": 1, "affectedSelectors": ["Language", "en-US", "Text", "if", "navigator.userAgent.match", "MSIE", "Internet", "Explorer", "i", "Trident", "r", "Is", "marked", "true"], "metadata": {"scanDuration": 441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-12T14:09:47.339Z"}}}, {"type": "info", "description": "Pronunciation guides found", "value": "Pronunciation elements: 0, Phonetic notations: 84", "element": "pronunciation elements", "elementCount": 1, "affectedSelectors": ["Pronunciation", "elements", "Phonetic", "notations"], "metadata": {"scanDuration": 441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-12T14:09:47.339Z"}}}], "recommendations": [], "executionTime": 53, "originalScore": 75, "adjustedScore": 75, "thresholdApplied": 70, "scoringDetails": "Original: 75% → Threshold: 70% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752329387339, "hash": "682f004a83fa9da36be525a8602cfc28", "accessCount": 1, "lastAccessed": 1752329387339, "size": 3289, "metadata": {"originalKey": "WCAG-065:053b13d2:add92319", "normalizedKey": "wcag-065_053b13d2_add92319", "savedAt": 1752329387340, "version": "1.1", "keyHash": "4fd5159d0fb41da2cf44adbbe0ab2652"}}