{"data": {"ruleId": "WCAG-029", "ruleName": "Page Titled", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.2.9", "level": "A", "status": "passed", "score": 90, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Page has descriptive title", "value": "<title>#1 Healthcare Collaboration Platform | TigerConnect</title>", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "Healthcare", "Collaboration", "Platform", "TigerConnect"], "severity": "info", "metadata": {"scanDuration": 2931, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-12T14:13:31.524Z"}}}, {"type": "warning", "description": "Multiple title elements found", "value": "14 title elements detected", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "elements", "detected"], "severity": "warning", "metadata": {"scanDuration": 2931, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-12T14:13:31.524Z"}}}], "recommendations": ["Use exactly one title element per page", "Use descriptive titles that identify the page topic and purpose", "Include both page-specific and site context in titles", "Keep titles between 10-60 characters for optimal display", "Test titles with screen readers and browser tabs"], "executionTime": 26, "originalScore": 90, "adjustedScore": 90, "thresholdApplied": 80, "scoringDetails": "Original: 90% → Threshold: 80% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752329611524, "hash": "e8e45054340b662326970e541f0832dd", "accessCount": 1, "lastAccessed": 1752329611524, "size": 1697, "metadata": {"originalKey": "WCAG-029:053b13d2:add92319", "normalizedKey": "wcag-029_053b13d2_add92319", "savedAt": 1752329611525, "version": "1.1", "keyHash": "a2e558d259f158ebff8bfc0e8ff98394"}}