{"data": {"landmarks": {"total": 5, "valid": 5, "foundTypes": ["main", "navigation", "navigation", "navigation", "banner"], "missing": ["contentinfo"], "duplicates": ["navigation"], "analysis": [{"type": "main", "selector": "#main", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "navigation", "selector": "#secondary-navigation", "hasLabel": true, "label": "Secondary Navigation", "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "navigation", "selector": "#site-navigation", "hasLabel": true, "label": "Primary Navigation", "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "navigation", "selector": "#mobile-site-navigation", "hasLabel": true, "label": "Primary Mobile Navigation", "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "banner", "selector": "#masthead", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}]}, "headings": {"total": 61, "structure": [{"level": 3, "text": "Who We Serve​", "selector": "div.wp-block-kadence-column.kadence-column419_53a1b5-b0 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column419_dbfa0f-fe > div.kt-inside-inner-col > h3.kt-adv-heading419_269ffe-a6.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Solutions​", "selector": "div.wp-block-kadence-column.kadence-column551_954935-98 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column551_494d60-05 > div.kt-inside-inner-col > h3.kt-adv-heading551_3d8df0-a8.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Resources", "selector": "div.wp-block-kadence-column.kadence-column553_d903bc-90 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column553_3c5abb-5a > div.kt-inside-inner-col > h3.kt-adv-heading553_520000-de.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Why TigerConnnect?", "selector": "div.wp-block-kadence-column.kadence-column555_b45fd4-81 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column555_23ef65-b7 > div.kt-inside-inner-col > h3.kt-adv-heading555_0f5d93-e9.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "One Platform to Unify Communications", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_ae5525-53 > div.kt-row-column-wrap.kt-has-1-columns > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > h1.kt-adv-heading15335_d2c3f3-b9.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Pre-Hospital", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_871db6-42 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Clinical Collaboration", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_79fe83-7c > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Physician<PERSON><PERSON>uling", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_09a7c5-e2 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Alarm Management & Event Notification", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_d4d585-5c > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "PatientEngagement", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_5d7cab-9c > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "CareConduit", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_1c3187-77 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Streamline Workflows Across the Care Continuum", "selector": "div.entry-content-wrap > div.entry-content.single-content > div.wp-block-kadence-column.kadence-column15335_ff5b9d-f6 > div.kt-inside-inner-col > h2.wp-block-heading.has-text-align-center", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 5, "text": "Critical Response", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_50f6e7-57 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "hasProperHierarchy": false, "isSkipped": true, "isEmpty": false, "issues": ["Heading level skipped (h2 to h5)"]}, {"level": 5, "text": "Emergency Department", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_7e3e63-9b > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 5, "text": "Inpatient Care", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_402b64-f7 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 5, "text": "Operating Room", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_cbf15a-02 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 5, "text": "Post Acute/Ambulatory", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_e56051-e1 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "The Future of Unified Healthcare Communications", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_605995-b4 > div.kt-row-column-wrap.kt-has-2-columns > div.wp-block-kadence-column.kadence-column15335_e6bf4b-2e > div.kt-inside-inner-col > h2.kt-adv-heading15335_c26c48-6d.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Improve Collaboration. <PERSON><PERSON><PERSON>ient Outcomes.", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_e78219-79 > div.kt-row-column-wrap.kt-has-1-columns > div.wp-block-kadence-column.kadence-column15335_9a192a-36 > div.kt-inside-inner-col > h2.kt-adv-heading15335_bc26ca-d1.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "20% increase", "selector": "div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_224dba-8d > div.kt-inside-inner-col > h3.kt-adv-heading15335_d64f68-08.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "50% reduction", "selector": "div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_bf8f55-5d > div.kt-inside-inner-col > h3.kt-adv-heading15335_895116-2f.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "50% faster", "selector": "div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_15a1e7-3a > div.kt-inside-inner-col > h3.kt-adv-heading15335_a33455-21.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "70% improvement", "selector": "div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_9ae7bf-ca > div.kt-inside-inner-col > h3.kt-adv-heading15335_6d345e-e7.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Improve the cost, quality, and overall experience of care.", "selector": "div.wp-block-kadence-column.kadence-column15335_7f890c-cc > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_6f8ab3-cc > div.kt-inside-inner-col > h3.kt-adv-heading15335_8b274d-54.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "2.5 min reduction", "selector": "div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_cc427a-7d > div.kt-inside-inner-col > h3.kt-adv-heading15335_bb6806-a5.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "72% improvement", "selector": "div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_492a04-34 > div.kt-inside-inner-col > h3.kt-adv-heading15335_9f2f72-40.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "20% reduction", "selector": "div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_2da758-37 > div.kt-inside-inner-col > h3.kt-adv-heading15335_8e6925-5c.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "46% improvement", "selector": "div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_ef2c29-cb > div.kt-inside-inner-col > h3.kt-adv-heading15335_562598-d7.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Solve Communication Challenges with a Single Platform", "selector": "div.entry-content-wrap > div.entry-content.single-content > div.wp-block-kadence-column.kadence-column15335_8af4d0-e8 > div.kt-inside-inner-col > h2.wp-block-heading.has-text-align-center", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Built With Your Care Teams in Mind", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_b17978-da > div.kt-row-column-wrap.kt-has-1-columns > div.wp-block-kadence-column.kadence-column15335_6597cd-c5 > div.kt-inside-inner-col > h2.kt-adv-heading15335_99b136-f9.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Empower Collaboration", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36 > div.kt-row-column-wrap.kt-has-4-columns > div.wp-block-kadence-column.kadence-column15335_db4e9d-bf > div.kt-inside-inner-col > h4.kt-adv-heading15335_21f0f1-46.wp-block-kadence-advancedheading", "hasProperHierarchy": false, "isSkipped": true, "isEmpty": false, "issues": ["Heading level skipped (h2 to h4)"]}, {"level": 4, "text": "Consolidate Your Technology", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36 > div.kt-row-column-wrap.kt-has-4-columns > div.wp-block-kadence-column.kadence-column15335_7573a1-93 > div.kt-inside-inner-col > h4.kt-adv-heading15335_e70d49-d4.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Minimize Administrative Tasks", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36 > div.kt-row-column-wrap.kt-has-4-columns > div.wp-block-kadence-column.kadence-column15335_b77a8f-16 > div.kt-inside-inner-col > h4.kt-adv-heading15335_54f200-c7.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Increase Efficiency", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36 > div.kt-row-column-wrap.kt-has-4-columns > div.wp-block-kadence-column.kadence-column15335_1e6dc7-f2 > div.kt-inside-inner-col > h4.kt-adv-heading15335_a41191-b0.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Trusted Partner in Unified Healthcare Communication", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_66a53f-25 > div.kt-row-column-wrap.kt-has-1-columns > div.wp-block-kadence-column.kadence-column15335_f2d02e-f0 > div.kt-inside-inner-col > h2.kt-adv-heading15335_62f142-8a.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Real Customers", "selector": "div.wp-block-kadence-column.kadence-column15335_428883-a4 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_a169c7-c1 > div.kt-inside-inner-col > h3.kt-adv-heading15335_ff9b3d-7f.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "“TigerConnect Clinical Collaboration Platform in general is very easy to use. […] Going from what we had before, which was managing manual spreadsheets and sending faxes and emails to groups, allowed us to make updates on the fly. It also gave the ability for the clinical staff to get that data timely versus the old process. TigerConnect Clinical Collaboration Platform was a huge improvement for us.”", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_886eb1-39.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Director", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_f0b6a3-dd.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "KLAS Research, October 2024", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_24f2f1-2c.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "“TigerConnect provides metrics so that we can monitor our utilization, and that is highly valuable. We recently deployed a functionality of roles, and that was very flexible. The vendor also has a messaging interface that we hope to integrate with our EHR so that we can send messages out of our EHR. Those functionalities are awesome.”", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_1286f6-2d.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Director", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_2ba86a-5c.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "KLAS Research, September 2024", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_a06166-ed.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "“One of the reasons TigerConnect was selected was that they have an ecosystem for middleware, alarm management, and things like that. I did not want to look at something that was only a messaging system. I wanted an ecosystem where things were integrated rather than bolted on.”", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_8fb36f-e9.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "CIO", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_be5d64-75.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "KLAS Research, October 2024", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_b7ce17-2d.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "“I am an extremely happy TigerConnect customer. I would give the ease of use a rating above the scale if I could. I would rate my ability to receive my money’s worth higher than the top of the scale if I could. Compared to our previous product, we actually paid less for TigerConnect Clinical Collaboration Platform.”", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_32d850-01.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "CIO", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_eda79f-32.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "KLAS Research, October 2024", "selector": "div.wp-block-kadence-slide.kb-advanced-slide-item > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_8324a9-77.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Clinical Communication & Collaboration Solution", "selector": "div.wp-block-kadence-column.kadence-column15335_78d048-64 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_b21d59-71 > div.kt-inside-inner-col > h3.kt-adv-heading15335_ab50f0-32.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "2024 Best in KLAS Winner", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_c867b4-54 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "#1 Leader in Winter 2025 G2 Rankings", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_a586cb-ee > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Named as a Leader and Placed Highest for Ability to Execute", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_dbae68-0c > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Resources", "selector": "div.kb-row-layout-wrap.kb-row-layout-id15335_89d1fd-36 > div.kt-row-column-wrap.kt-has-1-columns > div.wp-block-kadence-column.kadence-column15335_de65c4-4d > div.kt-inside-inner-col > h2.kt-adv-heading15335_a691f3-a8.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Explore Industry Insights & Reports", "selector": "div.wp-block-kadence-column.kadence-column15335_dbf649-90 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_da15c6-4c > div.kt-inside-inner-col > h2.kt-adv-heading15335_16577e-ed.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "State of Clinical Communications & Workflows", "selector": "div.wp-block-kadence-column.kadence-column15335_18fe5a-77 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_abd83e-4b > div.kt-inside-inner-col > h2.kt-adv-heading15335_20123a-da.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "2024 Gartner Magic Quadrant", "selector": "div.wp-block-kadence-column.kadence-column15335_e470f6-09 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_210d5b-5d > div.kt-inside-inner-col > h2.kt-adv-heading15335_f868b2-10.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Sales:", "selector": "div.wp-block-kadence-column.kadence-column22_ea6058-07 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_879323-31 > div.kt-inside-inner-col > h2.kt-adv-heading22_13bafe-ed.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "(800) 572-0470", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_9e9d94-53 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Email", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_e6603a-7c > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Support:", "selector": "div.wp-block-kadence-column.kadence-column22_ea6058-07 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_44aec1-1a > div.kt-inside-inner-col > h2.kt-adv-heading22_c8dd38-08.wp-block-kadence-advancedheading", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Email", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_2fabc2-28 > a.kt-blocks-info-box-link-wrap.info-box-link > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}], "hasH1": true, "hasProperHierarchy": false, "skippedLevels": [3, 4], "emptyHeadings": 0, "issues": ["Heading levels skipped"]}, "aria": {"total": 11, "valid": 11, "patterns": [{"pattern": "button", "element": "a", "selector": "nav.secondary-navigation.header-navigation > div.secondary-menu-container.header-menu-container > ul.menu > li.astm-search-menu.is-menu > a", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.secondary-menu-container.header-menu-container > ul.menu > li.astm-search-menu.is-menu > form.is-search-form.is-form-style > button.is-search-submit", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "#mobile-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "a", "selector": "div.kt-inside-inner-col > div.wp-block-kadence-videopopup.kadence-video-popup15335_a98dec-38 > div.kadence-video-popup-wrap.kadence-video-shadow > div.kadence-video-intrinsic > a.kadence-video-popup-link.kadence-video-type-external", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.popup-drawer.popup-drawer-layout-sidepanel > div.drawer-inner > div.drawer-header > button.menu-toggle-close.drawer-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom > div.drawer-nav-drop-wrap > button.drawer-sub-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom > div.drawer-nav-drop-wrap > button.drawer-sub-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type > div.drawer-nav-drop-wrap > button.drawer-sub-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type > div.drawer-nav-drop-wrap > button.drawer-sub-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom > div.drawer-nav-drop-wrap > button.drawer-sub-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom > div.drawer-nav-drop-wrap > button.drawer-sub-toggle", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}], "commonIssues": [], "invalidRoles": [], "missingLabels": []}, "semanticElements": {"total": 5, "used": ["header", "nav", "main", "article", "figure"], "missing": ["footer"], "misused": []}, "overallScore": 52, "criticalIssues": [], "recommendations": ["Add missing landmarks: contentinfo", "Fix heading hierarchy - avoid skipping levels", "Consider using semantic elements: footer"]}, "timestamp": 1752329332425, "hash": "504b20a7959460408479566ee4f7d872", "accessCount": 1, "lastAccessed": 1752329332425, "size": 27562, "metadata": {"originalKey": "https://tigerconnect.com/:semantic-validation-{\"validateLandmarks\":true,\"validateHeadings\":true,\"validateAria\":true,\"validateSemanticElements\":true,\"strictMode\":false,\"includeHiddenElements\":false}", "normalizedKey": "https_tigerconnect.com_semantic-validation-{_validatelandmarks_true,_validateheadings_true,_validatearia_true,_validatesemanticelements_true,_strictmode_false,_includehiddenelements_false}", "savedAt": 1752329332426, "version": "1.1", "keyHash": "4403c62e141ff21319f79636c1998433"}}