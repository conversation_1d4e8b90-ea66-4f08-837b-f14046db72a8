{"data": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 12, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-actuation-analysis", "motionEventDetection": true, "alternativeControlValidation": true, "advancedMotionDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-12T14:10:24.138Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "timestamp": 1752329424138, "hash": "e86247ab54080ce017ef9c974593fca8", "accessCount": 1, "lastAccessed": 1752329424138, "size": 848, "metadata": {"originalKey": "WCAG-056:WCAG-056", "normalizedKey": "wcag-056_wcag-056", "savedAt": 1752329424139, "version": "1.1", "keyHash": "1acf038a280dc3d3664b2e2cccd8a05c"}}