{"data": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 10, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-actuation-analysis", "motionEventDetection": true, "alternativeControlValidation": true, "advancedMotionDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-12T18:16:32.116Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "timestamp": 1752344192116, "hash": "2d8643383c3c16eea64dec6748b9315b", "accessCount": 1, "lastAccessed": 1752344192116, "size": 848, "metadata": {"originalKey": "WCAG-056:WCAG-056", "normalizedKey": "wcag-056_wcag-056", "savedAt": 1752344192117, "version": "1.1", "keyHash": "1acf038a280dc3d3664b2e2cccd8a05c"}}