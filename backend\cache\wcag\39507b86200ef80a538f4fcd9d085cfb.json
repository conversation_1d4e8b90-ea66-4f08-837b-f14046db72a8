{"data": [{"type": "text", "description": "Words that may need pronunciation guidance", "value": "Found 83 words that could benefit from pronunciation guidance for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "words", "that", "could", "benefit", "from", "pronunciation", "guidance", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.050Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Word may need pronunciation guidance: \"contact\"", "value": "Context: \"Contact Sales: (800) 572-0470\nContact\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.051Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "Contact", "Sales", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"tigerconnect\"", "value": "Context: \"ct Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerC\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ct", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "TigerC", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"wrap\"", "value": "Context: \"19_f82b81-6e > .kt-row-column-wrap{align-content:start;}:where(.\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "f82b81-6e", ".kt-row-column-wrap", "align-content", "start", "where", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"align\"", "value": "Context: \"2b81-6e > .kt-row-column-wrap{align-content:start;}:where(.kb-row\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "b81-6e", ".kt-row-column-wrap", "align-content", "start", "where", ".kb-row", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"left\"", "value": "Context: \"ntent-width, 1290px );padding-left:var(--global-content-edge-pad\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ntent-width", "px", "padding-left", "var", "global-content-edge-pad", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"right\"", "value": "Context: \"content-edge-padding);padding-right:var(--global-content-edge-pad\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "content-edge-padding", "padding-right", "var", "global-content-edge-pad", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"height\"", "value": "Context: \"obal-kb-spacing-md, 2rem);min-height:500px;}.kb-row-layout-id419_f\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "obal-kb-spacing-md", "rem", "min-height", "px", ".kb-row-layout-id419_f", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"isolation\"", "value": "Context: \"eft-radius:15px;overflow:clip;isolation:isolate;}.kb-row-layout-id419\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.052Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "eft-radius", "px", "overflow", "clip", "isolation", "isolate", ".kb-row-layout-id419", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"direction\"", "value": "Context: \"0 > .kt-inside-inner-col{flex-direction:column;}.kadence-column419_53\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.053Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", ".kt-inside-inner-col", "flex-direction", "column", ".kadence-column419_53", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"aligncenter\"", "value": "Context: \"-b0 > .kt-inside-inner-col > .aligncenter{width:100%;}.kadence-column41\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 143485, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-12T14:13:05.053Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "b0", ".kt-inside-inner-col", ".aligncenter", "width", ".kadence-column41", "Category", "complex-word"]}], "timestamp": 1752329585053, "hash": "df11d284b7ef91beb6c3fdc40f2e105f", "accessCount": 1, "lastAccessed": 1752329585053, "size": 10316, "metadata": {"originalKey": "WCAG-063:WCAG-063", "normalizedKey": "wcag-063_wcag-063", "savedAt": 1752329585055, "version": "1.1", "keyHash": "e416d4b0f627f598fd1a1b6c2e41d77d"}}