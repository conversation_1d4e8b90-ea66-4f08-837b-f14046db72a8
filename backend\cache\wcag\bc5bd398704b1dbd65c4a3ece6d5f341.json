{"data": {"ruleId": "WCAG-022", "ruleName": "Accessible Authentication (Minimum)", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.05, "automated": false, "evidence": [{"type": "error", "description": "Error during authentication analysis", "value": "this.getElementSelector is not a function\npptr:evaluate;AccessibleAuthenticationCheck.detectCognitiveFunctionTests%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Caccessible-authentication.ts%3A352%3A27):19:40\nNodeList.forEach (<anonymous>)", "severity": "error", "elementCount": 1, "affectedSelectors": ["this.getElementSelector", "is", "not", "a", "function", "pptr", "evaluate", "AccessibleAuthenticationCheck.detectCognitiveFunctionTests", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Caccessible-authentication.ts", "A352", "A27", "NodeList.forEach", "anonymous"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-022", "ruleName": "Accessible Authentication (Minimum)", "timestamp": "2025-07-12T18:15:53.620Z"}}}], "recommendations": ["Review authentication mechanisms manually for accessibility compliance"], "executionTime": 0, "errorMessage": "Failed to analyze authentication accessibility", "manualReviewItems": []}, "timestamp": 1752344153620, "hash": "efd8c04752004751b0729138cae37a18", "accessCount": 1, "lastAccessed": 1752344153620, "size": 1426, "metadata": {"originalKey": "WCAG-022:053b13d2:add92319", "normalizedKey": "wcag-022_053b13d2_add92319", "savedAt": 1752344153621, "version": "1.1", "keyHash": "8ce2c48713b5953853c8174efae8e23f"}}