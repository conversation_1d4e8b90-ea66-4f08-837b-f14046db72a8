{"data": {"ruleId": "WCAG-025", "ruleName": "Landmarks", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.2.5", "level": "A", "status": "passed", "score": 75, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Advanced layout analysis for landmark structure validation", "element": "landmark-elements", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":1450,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1598, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-12T14:13:25.481Z"}}}, {"type": "warning", "description": "Content elements outside landmark regions", "value": "210 elements not in landmarks", "selector": "p, h1, h2, h3, h4, h5, h6, article, section, div", "elementCount": 10, "affectedSelectors": ["p", "h1", "h2", "h3", "h4", "h5", "h6", "article", "section", "div", "elements", "not", "in", "landmarks"], "severity": "warning", "metadata": {"scanDuration": 1598, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-12T14:13:25.483Z"}}}], "recommendations": ["Move content into appropriate landmark regions", "Use semantic HTML5 landmark elements (main, nav, header, footer, aside)", "Ensure all content is contained within appropriate landmarks", "Provide accessible names for multiple landmarks of the same type"], "executionTime": 1488, "originalScore": 75, "adjustedScore": 75, "thresholdApplied": 80, "scoringDetails": "Original: 75% → Threshold: 80% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752329605483, "hash": "c685810efee78f73348f17165cf6a693", "accessCount": 1, "lastAccessed": 1752329605483, "size": 2129, "metadata": {"originalKey": "WCAG-025:053b13d2:add92319", "normalizedKey": "wcag-025_053b13d2_add92319", "savedAt": 1752329605483, "version": "1.1", "keyHash": "c55b6d591428c44d5ef6f44e5238a40a"}}