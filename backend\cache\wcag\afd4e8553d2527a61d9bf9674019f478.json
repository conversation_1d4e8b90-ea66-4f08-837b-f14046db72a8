{"data": {"ruleId": "WCAG-031", "ruleName": "Error Suggestion", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.3.1", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "code", "description": "Form 1 needs better error recovery", "value": "form:nth-of-type(1) - recovery features: 0/4 (undo: false, autosave: false, confirmation: false, summary: false)", "selector": "form:nth-of-type(1)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["form:nth-of-type(1)", "form", "nth-of-type", "recovery", "features", "undo", "false", "autosave", "confirmation", "summary"], "metadata": {"scanDuration": 1001, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-031", "ruleName": "Error Suggestion", "timestamp": "2025-07-12T14:13:33.044Z"}}}], "recommendations": ["Add error recovery mechanisms to form 1 (undo, autosave, confirmation, or validation summary)"], "executionTime": 68, "originalScore": 0, "adjustedScore": 0, "thresholdApplied": 70, "scoringDetails": "Original: 0% → Threshold: 70% → Penalty tier: 0%+ (0x) → FAILED", "penaltyTier": 0, "confidenceAdjustment": 1, "enhancedStatus": "failed"}, "timestamp": 1752329613044, "hash": "ce26b9a95c780e1cc9838b2e68b87c74", "accessCount": 1, "lastAccessed": 1752329613044, "size": 1213, "metadata": {"originalKey": "WCAG-031:053b13d2:add92319", "normalizedKey": "wcag-031_053b13d2_add92319", "savedAt": 1752329613045, "version": "1.1", "keyHash": "e88702252da201b3527a9df515cbf2e5"}}