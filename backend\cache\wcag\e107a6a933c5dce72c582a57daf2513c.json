{"data": {"ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.4.5", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 377, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-12T14:09:49.872Z"}}}], "recommendations": ["Provide pause, stop, or hide controls for moving content", "Allow users to control auto-updating content", "Ensure moving content can be paused on user request", "Consider reducing or eliminating unnecessary animations"], "executionTime": 41, "originalScore": 0, "adjustedScore": 0, "thresholdApplied": 80, "scoringDetails": "Original: 0% → Threshold: 80% → Penalty tier: 0%+ (0x) → FAILED", "penaltyTier": 0, "confidenceAdjustment": 1, "enhancedStatus": "failed"}, "timestamp": 1752329389872, "hash": "b4403274cfd8dfa7ce07c34c55060e02", "accessCount": 1, "lastAccessed": 1752329389872, "size": 1187, "metadata": {"originalKey": "WCAG-045:053b13d2:add92319", "normalizedKey": "wcag-045_053b13d2_add92319", "savedAt": 1752329389873, "version": "1.1", "keyHash": "7b43cd99526d17cceac5e8e4d08aa542"}}