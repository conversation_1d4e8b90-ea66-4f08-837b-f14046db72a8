{"data": {"ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.5.9", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "code", "description": "Input modality detection: high risk", "value": "Touch: 2, <PERSON>: 0, Keyboard: 0, Pointer: 38", "severity": "error", "elementCount": 1, "affectedSelectors": ["Touch", "Mouse", "Keyboard", "Pointer"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "text", "description": "Detected input restriction types", "value": "pointer-disabled, css-pointer-disabled, css-touch-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["pointer-disabled", "css-pointer-disabled", "css-touch-disabled"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Restriction analysis: high severity restrictions", "value": "Methods: pointer-events-none, user-select-none, javascript-prevention, Affected elements: 604, Bypass mechanisms: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Methods", "pointer-events-none", "user-select-none", "javascript-prevention", "Affected", "elements", "Bypass", "mechanisms"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Multi-modal interaction testing: poor rating", "value": "Touch: false, Mouse: true, Keyboard: true, Pointer: true, Concurrent: false", "severity": "error", "elementCount": 1, "affectedSelectors": ["Touch", "false", "Mouse", "true", "Keyboard", "Pointer", "Concurrent"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 1 needs improvement", "value": "button:nth-of-type(1) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 2 needs improvement", "value": "a:nth-of-type(2) - supported methods: 0, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["a", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 3 needs improvement", "value": "input:nth-of-type(3) - supported methods: 1, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["input", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 4 needs improvement", "value": "button:nth-of-type(4) - supported methods: 1, accessible: Search Button", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Search", "<PERSON><PERSON>"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 5 needs improvement", "value": "input:nth-of-type(5) - supported methods: 1, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["input", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 6 needs improvement", "value": "button:nth-of-type(6) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 7 needs improvement", "value": "button:nth-of-type(7) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 8 needs improvement", "value": "button:nth-of-type(8) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 9 needs improvement", "value": "button:nth-of-type(9) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 10 needs improvement", "value": "button:nth-of-type(10) - supported methods: 1, accessible: \n\t\t\t\t\t\tToggle Menu\n\t\t\t\t\n\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "<PERSON><PERSON>"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 11 needs improvement", "value": "button:nth-of-type(11) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 12 needs improvement", "value": "button:nth-of-type(12) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 13 needs improvement", "value": "button:nth-of-type(13) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 14 needs improvement", "value": "button:nth-of-type(14) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 15 needs improvement", "value": "button:nth-of-type(15) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 16 needs improvement", "value": "button:nth-of-type(16) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 17 needs improvement", "value": "button:nth-of-type(17) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 18 needs improvement", "value": "button:nth-of-type(18) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.011Z"}}}, {"type": "code", "description": "Input element 19 needs improvement", "value": "a:nth-of-type(19) - supported methods: 0, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["a", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 20 needs improvement", "value": "button:nth-of-type(20) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 21 needs improvement", "value": "button:nth-of-type(21) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 22 needs improvement", "value": "button:nth-of-type(22) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 23 needs improvement", "value": "button:nth-of-type(23) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 24 needs improvement", "value": "button:nth-of-type(24) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 25 needs improvement", "value": "button:nth-of-type(25) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 26 needs improvement", "value": "button:nth-of-type(26) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 27 needs improvement", "value": "button:nth-of-type(27) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 28 needs improvement", "value": "button:nth-of-type(28) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 29 needs improvement", "value": "button:nth-of-type(29) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 30 needs improvement", "value": "button:nth-of-type(30) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 31 needs improvement", "value": "button:nth-of-type(31) - supported methods: 1, accessible: \n\t\t\t\t\t\n\t\t\t\t\t\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 32 needs improvement", "value": "button:nth-of-type(32) - supported methods: 1, accessible: Toggle child menuExpand\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "child", "menuExpand"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 33 needs improvement", "value": "button:nth-of-type(33) - supported methods: 1, accessible: Toggle child menuExpand\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "child", "menuExpand"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 34 needs improvement", "value": "button:nth-of-type(34) - supported methods: 1, accessible: Toggle child menuExpand\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "child", "menuExpand"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 35 needs improvement", "value": "button:nth-of-type(35) - supported methods: 1, accessible: Toggle child menuExpand\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "child", "menuExpand"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 36 needs improvement", "value": "button:nth-of-type(36) - supported methods: 1, accessible: Toggle child menuExpand\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "child", "menuExpand"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}, {"type": "code", "description": "Input element 37 needs improvement", "value": "button:nth-of-type(37) - supported methods: 1, accessible: Toggle child menuExpand\n\t\t\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "child", "menuExpand"], "metadata": {"scanDuration": 502, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-12T14:10:27.012Z"}}}], "recommendations": ["Remove or reduce input modality restrictions to support concurrent input mechanisms", "Add bypass mechanisms for input restrictions or reduce restriction severity", "Improve multi-modal interaction support for better accessibility", "Improve multi-modal support for input element 1", "Improve accessibility and multi-modal support for input element 2", "Improve accessibility and multi-modal support for input element 3", "Improve multi-modal support for input element 4", "Improve accessibility and multi-modal support for input element 5", "Improve multi-modal support for input element 6", "Improve multi-modal support for input element 7", "Improve multi-modal support for input element 8", "Improve multi-modal support for input element 9", "Improve multi-modal support for input element 10", "Improve multi-modal support for input element 11", "Improve multi-modal support for input element 12", "Improve multi-modal support for input element 13", "Improve multi-modal support for input element 14", "Improve multi-modal support for input element 15", "Improve multi-modal support for input element 16", "Improve multi-modal support for input element 17", "Improve multi-modal support for input element 18", "Improve accessibility and multi-modal support for input element 19", "Improve multi-modal support for input element 20", "Improve multi-modal support for input element 21", "Improve multi-modal support for input element 22", "Improve multi-modal support for input element 23", "Improve multi-modal support for input element 24", "Improve multi-modal support for input element 25", "Improve multi-modal support for input element 26", "Improve multi-modal support for input element 27", "Improve multi-modal support for input element 28", "Improve multi-modal support for input element 29", "Improve multi-modal support for input element 30", "Improve multi-modal support for input element 31", "Improve multi-modal support for input element 32", "Improve multi-modal support for input element 33", "Improve multi-modal support for input element 34", "Improve multi-modal support for input element 35", "Improve multi-modal support for input element 36", "Improve multi-modal support for input element 37"], "executionTime": 136, "originalScore": 0, "adjustedScore": 0, "thresholdApplied": 80, "scoringDetails": "Original: 0% → Threshold: 80% → Penalty tier: 0%+ (0x) → FAILED", "penaltyTier": 0, "confidenceAdjustment": 1, "enhancedStatus": "failed"}, "timestamp": 1752329427012, "hash": "10abb0500e59c63ce7e711d37c5d6389", "accessCount": 1, "lastAccessed": 1752329427012, "size": 24178, "metadata": {"originalKey": "WCAG-059:053b13d2:add92319", "normalizedKey": "wcag-059_053b13d2_add92319", "savedAt": 1752329427013, "version": "1.1", "keyHash": "8ed38c81908130d34a7dc438eda5779b"}}