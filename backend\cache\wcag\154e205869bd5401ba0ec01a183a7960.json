{"data": {"ruleId": "WCAG-036", "ruleName": "Headings and Labels", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.3.6", "level": "AA", "status": "passed", "score": 85, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "text", "description": "Heading analysis: undefined", "value": "Who We Serve​", "selector": "h3:nth-of-type(1)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(1)", "Who", "We", "Serve"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Solutions​", "selector": "h3:nth-of-type(2)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(2)", "Solutions"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Resources", "selector": "h3:nth-of-type(3)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(3)", "Resources"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Why TigerConnnect?", "selector": "h3:nth-of-type(4)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(4)", "Why", "TigerConnnect"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "One Platform to Unify Communications", "selector": "h1:nth-of-type(5)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h1:nth-of-type(5)", "One", "Platform", "to", "Unify", "Communications"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Pre-Hospital", "selector": "h2:nth-of-type(6)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(6)", "Pre-Hospital"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Clinical Collaboration", "selector": "h2:nth-of-type(7)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(7)", "Clinical", "Collaboration"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Physician<PERSON><PERSON>uling", "selector": "h2:nth-of-type(8)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(8)", "Physician<PERSON><PERSON>uling"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Alarm Management & Event Notification", "selector": "h2:nth-of-type(9)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(9)", "Alarm", "Management", "Event", "Notification"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "PatientEngagement", "selector": "h2:nth-of-type(10)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(10)", "PatientEngagement"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.965Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "CareConduit", "selector": "h2:nth-of-type(11)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(11)", "CareConduit"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Streamline Workflows Across the Care Continuum", "selector": "h2:nth-of-type(12)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(12)", "Streamline", "Workflows", "Across", "the", "Care", "Continuum"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Critical Response", "selector": "h5:nth-of-type(13)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(13)", "Critical", "Response"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Emergency Department", "selector": "h5:nth-of-type(14)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(14)", "Emergency", "Department"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Inpatient Care", "selector": "h5:nth-of-type(15)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(15)", "Inpatient", "Care"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Operating Room", "selector": "h5:nth-of-type(16)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(16)", "Operating", "Room"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Post Acute/Ambulatory", "selector": "h5:nth-of-type(17)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(17)", "Post", "Acute", "Ambulatory"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "The Future of Unified Healthcare Communications", "selector": "h2:nth-of-type(18)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(18)", "The", "Future", "of", "Unified", "Healthcare", "Communications"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Improve Collaboration. <PERSON><PERSON><PERSON>ient Outcomes.", "selector": "h2:nth-of-type(19)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(19)", "Improve", "Collaboration", "<PERSON><PERSON>ce", "Patient", "Outcomes"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "20% increase", "selector": "h3:nth-of-type(20)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(20)", "increase"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "50% reduction", "selector": "h3:nth-of-type(21)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(21)", "reduction"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "50% faster", "selector": "h3:nth-of-type(22)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(22)", "faster"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "70% improvement", "selector": "h3:nth-of-type(23)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(23)", "improvement"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Improve the cost, quality, and overall experience of care.", "selector": "h3:nth-of-type(24)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(24)", "Improve", "the", "cost", "quality", "and", "overall", "experience", "of", "care"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "2.5 min reduction", "selector": "h3:nth-of-type(25)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(25)", "min", "reduction"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "72% improvement", "selector": "h3:nth-of-type(26)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(26)", "improvement"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "20% reduction", "selector": "h3:nth-of-type(27)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(27)", "reduction"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "46% improvement", "selector": "h3:nth-of-type(28)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(28)", "improvement"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Solve Communication Challenges with a Single Platform", "selector": "h2:nth-of-type(29)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(29)", "Solve", "Communication", "Challenges", "with", "a", "Single", "Platform"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Built With Your Care Teams in Mind", "selector": "h2:nth-of-type(30)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(30)", "Built", "With", "Your", "Care", "Teams", "in", "Mind"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Empower Collaboration", "selector": "h4:nth-of-type(31)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(31)", "Empower", "Collaboration"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Consolidate Your Technology", "selector": "h4:nth-of-type(32)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(32)", "Consolidate", "Your", "Technology"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Minimize Administrative Tasks", "selector": "h4:nth-of-type(33)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(33)", "Minimize", "Administrative", "Tasks"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Increase Efficiency", "selector": "h4:nth-of-type(34)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(34)", "Increase", "Efficiency"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Trusted Partner in Unified Healthcare Communication", "selector": "h2:nth-of-type(35)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(35)", "Trusted", "Partner", "in", "Unified", "Healthcare", "Communication"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Real Customers", "selector": "h3:nth-of-type(36)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(36)", "Real", "Customers"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "“TigerConnect Clinical Collaboration Platform in general is very easy to use. […] Going from what we had before, which was managing manual spreadsheets and sending faxes and emails to groups, allowed us to make updates on the fly. It also gave the ability for the clinical staff to get that data timely versus the old process. TigerConnect Clinical Collaboration Platform was a huge improvement for us.”", "selector": "h2:nth-of-type(37)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(37)", "TigerConnect", "Clinical", "Collaboration", "Platform", "in", "general", "is", "very", "easy", "to", "use", "Going", "from", "what", "we", "had", "before", "which", "was", "managing", "manual", "spreadsheets", "and", "sending", "faxes", "emails", "groups", "allowed", "us", "make", "updates", "on", "the", "fly", "It", "also", "gave", "ability", "for", "clinical", "staff", "get", "that", "data", "timely", "versus", "old", "process", "a", "huge", "improvement"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Director", "selector": "h2:nth-of-type(38)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(38)", "Director"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "KLAS Research, October 2024", "selector": "h2:nth-of-type(39)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(39)", "KLAS", "Research", "October"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "“TigerConnect provides metrics so that we can monitor our utilization, and that is highly valuable. We recently deployed a functionality of roles, and that was very flexible. The vendor also has a messaging interface that we hope to integrate with our EHR so that we can send messages out of our EHR. Those functionalities are awesome.”", "selector": "h2:nth-of-type(40)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(40)", "TigerConnect", "provides", "metrics", "so", "that", "we", "can", "monitor", "our", "utilization", "and", "is", "highly", "valuable", "We", "recently", "deployed", "a", "functionality", "of", "roles", "was", "very", "flexible", "The", "vendor", "also", "has", "messaging", "interface", "hope", "to", "integrate", "with", "EHR", "send", "messages", "out", "Those", "functionalities", "are", "awesome"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Director", "selector": "h2:nth-of-type(41)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(41)", "Director"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "KLAS Research, September 2024", "selector": "h2:nth-of-type(42)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(42)", "KLAS", "Research", "September"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "“One of the reasons TigerConnect was selected was that they have an ecosystem for middleware, alarm management, and things like that. I did not want to look at something that was only a messaging system. I wanted an ecosystem where things were integrated rather than bolted on.”", "selector": "h2:nth-of-type(43)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(43)", "One", "of", "the", "reasons", "TigerConnect", "was", "selected", "that", "they", "have", "an", "ecosystem", "for", "middleware", "alarm", "management", "and", "things", "like", "I", "did", "not", "want", "to", "look", "at", "something", "only", "a", "messaging", "system", "wanted", "where", "were", "integrated", "rather", "than", "bolted", "on"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "CIO", "selector": "h2:nth-of-type(44)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(44)", "CIO"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "KLAS Research, October 2024", "selector": "h2:nth-of-type(45)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(45)", "KLAS", "Research", "October"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "“I am an extremely happy TigerConnect customer. I would give the ease of use a rating above the scale if I could. I would rate my ability to receive my money’s worth higher than the top of the scale if I could. Compared to our previous product, we actually paid less for TigerConnect Clinical Collaboration Platform.”", "selector": "h2:nth-of-type(46)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(46)", "I", "am", "an", "extremely", "happy", "TigerConnect", "customer", "would", "give", "the", "ease", "of", "use", "a", "rating", "above", "scale", "if", "could", "rate", "my", "ability", "to", "receive", "money", "s", "worth", "higher", "than", "top", "Compared", "our", "previous", "product", "we", "actually", "paid", "less", "for", "Clinical", "Collaboration", "Platform"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "CIO", "selector": "h2:nth-of-type(47)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(47)", "CIO"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "KLAS Research, October 2024", "selector": "h2:nth-of-type(48)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(48)", "KLAS", "Research", "October"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Clinical Communication & Collaboration Solution", "selector": "h3:nth-of-type(49)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(49)", "Clinical", "Communication", "Collaboration", "Solution"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "2024 Best in KLAS Winner", "selector": "h2:nth-of-type(50)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(50)", "Best", "in", "KLAS", "Winner"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "#1 Leader in Winter 2025 G2 Rankings", "selector": "h2:nth-of-type(51)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(51)", "Leader", "in", "Winter", "G2", "Rankings"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Named as a Leader and Placed Highest for Ability to Execute", "selector": "h2:nth-of-type(52)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(52)", "Named", "as", "a", "Leader", "and", "Placed", "Highest", "for", "Ability", "to", "Execute"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Resources", "selector": "h2:nth-of-type(53)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(53)", "Resources"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Explore Industry Insights & Reports", "selector": "h2:nth-of-type(54)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(54)", "Explore", "Industry", "Insights", "Reports"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "State of Clinical Communications & Workflows", "selector": "h2:nth-of-type(55)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(55)", "State", "of", "Clinical", "Communications", "Workflows"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "2024 Gartner Magic Quadrant", "selector": "h2:nth-of-type(56)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(56)", "<PERSON><PERSON><PERSON>", "Magic", "Quadrant"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Sales:", "selector": "h2:nth-of-type(57)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(57)", "Sales"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "(800) 572-0470", "selector": "h2:nth-of-type(58)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(58)"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Email", "selector": "h2:nth-of-type(59)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(59)", "Email"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Support:", "selector": "h2:nth-of-type(60)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(60)", "Support"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "Email", "selector": "h2:nth-of-type(61)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(61)", "Email"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "TigerConnect Launches New Cloud-Based Integration and Workflow Automation Engine", "selector": "h2:nth-of-type(62)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(62)", "TigerConnect", "Launches", "New", "Cloud-Based", "Integration", "and", "Workflow", "Automation", "Engine"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "07/16/2025 | Webinar |  Thriving in a Cerner Environment with TigerConnect", "selector": "h2:nth-of-type(63)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(63)", "Webinar", "Thriving", "in", "a", "<PERSON><PERSON>", "Environment", "with", "TigerConnect"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}, {"type": "text", "description": "Heading analysis: undefined", "value": "07/29/2025 | Webinar | Simplifying System Integrations: Leveraging EHR Data to Accelerate Speed to Care", "selector": "h2:nth-of-type(64)", "severity": "info", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(64)", "Webinar", "Simplifying", "System", "Integrations", "Leveraging", "EHR", "Data", "to", "Accelerate", "Speed", "Care"], "metadata": {"scanDuration": 115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-12T14:13:37.966Z"}}}], "recommendations": ["Ensure proper heading hierarchy (H1 → H2 → H3, etc.)", "Use descriptive heading text that summarizes section content"], "executionTime": 35, "originalScore": 85, "adjustedScore": 85, "thresholdApplied": 80, "scoringDetails": "Original: 85% → Threshold: 80% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752329617966, "hash": "453075319eb283257338efd87fadada4", "accessCount": 1, "lastAccessed": 1752329617966, "size": 33704, "metadata": {"originalKey": "WCAG-036:053b13d2:add92319", "normalizedKey": "wcag-036_053b13d2_add92319", "savedAt": 1752329617968, "version": "1.1", "keyHash": "e9548dcff4860d1243c04cc18074df3c"}}