{"data": [{"type": "text", "description": "Element has accessible name", "value": "Name: \"Skip to content\"", "selector": "a.skip-link.screen-reader-text.scroll-ignore", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.skip-link.screen-reader-text.scroll-ignore", "Name", "<PERSON><PERSON>", "to", "content"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.skip-link.screen-reader-text.scroll-ignore", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.skip-link.screen-reader-text.scroll-ignore", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.skip-link.screen-reader-text.scroll-ignore", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.skip-link.screen-reader-text.scroll-ignore", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Contact Sales: (800) 572-0470\"", "selector": "a:nth-of-type(2)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "Name", "Contact", "Sales"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(2)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(2)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Contact Support\"", "selector": "a:nth-of-type(3)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "Name", "Contact", "Support"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(3)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(3)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"LoginExpand\"", "selector": "a:nth-of-type(4)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(4)", "Name", "LoginExpand"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(4)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(4)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(4)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(4)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Child menu\"", "selector": "button.dropdown-nav-special-toggle", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.dropdown-nav-special-toggle", "Name", "Child", "menu"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.590Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.dropdown-nav-special-toggle", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.dropdown-nav-special-toggle", "Role", "button"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.dropdown-nav-special-toggle", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.dropdown-nav-special-toggle", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"TigerConnect\"", "selector": "a:nth-of-type(6)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "Name", "TigerConnect"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(6)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(6)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Physician <PERSON>\"", "selector": "a:nth-of-type(7)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(7)", "Name", "Physician", "Scheduling"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(7)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(7)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(7)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(7)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"TigerConnect Community\"", "selector": "a:nth-of-type(8)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(8)", "Name", "TigerConnect", "Community"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(8)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(8)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(8)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(8)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Search Icon Link\"", "selector": "a:nth-of-type(9)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(9)", "Name", "Search", "Icon", "Link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "a:nth-of-type(9)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(9)", "Role", "button"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Search for:\"", "selector": "#is-search-input-3779", "severity": "info", "elementCount": 1, "affectedSelectors": ["#is-search-input-3779", "Name", "Search", "for"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: textbox", "selector": "#is-search-input-3779", "severity": "info", "elementCount": 1, "affectedSelectors": ["#is-search-input-3779", "Role", "textbox"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "#is-search-input-3779", "severity": "info", "elementCount": 1, "affectedSelectors": ["#is-search-input-3779", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<PERSON> Button\"", "selector": "button.is-search-submit", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.is-search-submit", "Name", "Search", "<PERSON><PERSON>"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 30, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.is-search-submit", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.is-search-submit", "Role", "button"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 31, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.is-search-submit", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.is-search-submit", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 32, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.brand.has-logo-image", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.brand.has-logo-image", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 33, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.brand.has-logo-image", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.brand.has-logo-image", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 34, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Home\"", "selector": "a:nth-of-type(13)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(13)", "Name", "Home"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 35, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(13)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(13)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 36, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(13)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(13)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 37, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Who We ServeExpand\"", "selector": "a:nth-of-type(14)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(14)", "Name", "Who", "We", "ServeExpand"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 38, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a:nth-of-type(14)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(14)", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 39, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.591Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a:nth-of-type(14)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(14)", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 40, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Child menu of Who We Serve\"", "selector": "button.dropdown-nav-special-toggle", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.dropdown-nav-special-toggle", "Name", "Child", "menu", "of", "Who", "We", "Serve"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 41, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.dropdown-nav-special-toggle", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.dropdown-nav-special-toggle", "Role", "button"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 42, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.dropdown-nav-special-toggle", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.dropdown-nav-special-toggle", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 43, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.kb-section-link-overlay", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-section-link-overlay", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 44, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.kb-section-link-overlay", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kb-section-link-overlay", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 45, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Services\"", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Name", "Services"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 46, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 47, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left", "State", "value", "information", "available"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 48, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Professional Services\"", "selector": "a.kt-svg-icon-link", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-svg-icon-link", "Name", "Professional", "Services"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 49, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.kt-svg-icon-link", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.kt-svg-icon-link", "Role", "link"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 981, "checkSpecificData": {"automationRate": 0.9, "checkType": "aria-component-analysis", "manualReviewRequired": false, "interactiveElementAnalysis": true, "ariaImplementationAnalysis": true, "customComponentAnalysis": true, "evidenceIndex": 50, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-12T18:15:18.592Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752344118594, "hash": "7a61c7bb082bdb4368a31aa833e48525", "accessCount": 1, "lastAccessed": 1752344118594, "size": 40071, "metadata": {"originalKey": "WCAG-009:WCAG-009", "normalizedKey": "wcag-009_wcag-009", "savedAt": 1752344118595, "version": "1.1", "keyHash": "449d28e719c4c1826c6c973f468abd5f"}}