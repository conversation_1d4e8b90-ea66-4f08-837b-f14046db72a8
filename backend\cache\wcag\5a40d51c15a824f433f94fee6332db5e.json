{"data": {"ruleId": "WCAG-064", "ruleName": "Change on Request", "category": "predictable", "wcagVersion": "2.2", "successCriterion": "0.6.4", "level": "AAA", "status": "partial", "score": 30, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "code", "description": "Context change detection: high risk", "value": "Unexpected: 24, User-controlled: 0, Automatic: 0, Warnings: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Unexpected", "User-controlled", "Automatic", "Warnings"], "metadata": {"scanDuration": 908, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:51.988Z"}}}, {"type": "text", "description": "Detected context change types", "value": "new-window", "severity": "info", "elementCount": 1, "affectedSelectors": ["new-window"], "metadata": {"scanDuration": 908, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:51.989Z"}}}, {"type": "code", "description": "User control validation: Inadequate control mechanisms", "value": "Available controls: none (need at least 2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["Available", "controls", "none", "need", "at", "least"], "metadata": {"scanDuration": 908, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:51.989Z"}}}, {"type": "text", "description": "Unexpected change analysis: Low risk of unexpected changes", "value": "Risk assessment: low, Score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "assessment", "low", "Score"], "metadata": {"scanDuration": 908, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:51.989Z"}}}, {"type": "text", "description": "Accessibility control testing: No context change controls detected", "value": "No context change controls found to test", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "context", "change", "controls", "found", "to", "test"], "metadata": {"scanDuration": 908, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T18:19:51.989Z"}}}], "recommendations": ["Add user control and warnings for context changes", "Add user control mechanisms: confirmations, warnings, or disable options"], "executionTime": 262, "originalScore": 50, "adjustedScore": 50, "thresholdApplied": 75, "scoringDetails": "Original: 50% → Threshold: 75% → Penalty tier: 45%+ (0.6x) → PARTIAL", "penaltyTier": 0.6, "confidenceAdjustment": 1, "enhancedStatus": "partial"}, "timestamp": 1752344391989, "hash": "368063e018d2245c4e9a4ef9ebbf05d7", "accessCount": 1, "lastAccessed": 1752344391989, "size": 2946, "metadata": {"originalKey": "WCAG-064:053b13d2:add92319", "normalizedKey": "wcag-064_053b13d2_add92319", "savedAt": 1752344391990, "version": "1.1", "keyHash": "068b17f4e1323d7fa24ab85adbc3cecc"}}