{"data": {"ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.4.4", "level": "A", "status": "passed", "score": 75, "maxScore": 100, "weight": 0.0687, "automated": true, "evidence": [{"type": "code", "description": "Timing pattern detection: high risk", "value": "Timeouts: 0, Sessions: 0, Auto-refresh: 0, Countdowns: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Timeouts", "Sessions", "Auto-refresh", "Countdowns"], "metadata": {"scanDuration": 370, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-12T18:15:54.793Z"}}}, {"type": "text", "description": "Detected timing patterns", "value": "JavaScript timers, Auto-refresh, Countdown timer, Session timeout", "severity": "info", "elementCount": 1, "affectedSelectors": ["JavaScript", "timers", "Auto-refresh", "Countdown", "timer", "Session", "timeout"], "metadata": {"scanDuration": 370, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-12T18:15:54.793Z"}}}, {"type": "text", "description": "Timeout control validation: Adequate controls available", "value": "Controls: none needed", "severity": "info", "elementCount": 1, "affectedSelectors": ["Controls", "none", "needed"], "metadata": {"scanDuration": 370, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-12T18:15:54.793Z"}}}, {"type": "text", "description": "Session management analysis: Low risk session management", "value": "Risk level: low, Timeout: 0s", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "level", "low", "Timeout", "s"], "metadata": {"scanDuration": 370, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-12T18:15:54.793Z"}}}, {"type": "text", "description": "Auto-refresh detection: No problematic auto-refresh detected", "value": "No auto-refresh elements found", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "auto-refresh", "elements", "found"], "metadata": {"scanDuration": 370, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-12T18:15:54.793Z"}}}], "recommendations": ["Add user controls for timing-based content (extend, disable, or adjust)"], "executionTime": 25, "originalScore": 75, "adjustedScore": 75, "thresholdApplied": 80, "scoringDetails": "Original: 75% → Threshold: 80% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752344154793, "hash": "9cefcb0fdf54069cb18d8470cb20ef8b", "accessCount": 1, "lastAccessed": 1752344154793, "size": 2903, "metadata": {"originalKey": "WCAG-044:053b13d2:add92319", "normalizedKey": "wcag-044_053b13d2_add92319", "savedAt": 1752344154794, "version": "1.1", "keyHash": "e7cde8f2001f70df4198c2b4268e9fbd"}}