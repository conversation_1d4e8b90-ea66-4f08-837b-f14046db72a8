{"data": [{"type": "info", "description": "Valid skip link found", "value": "Skip link: \"Skip to content\" targeting main", "selector": "a[href^=\"#\"]:first-child:nth-of-type(1)", "elementCount": 1, "affectedSelectors": ["a[href^=\"#\"]:first-child:nth-of-type(1)", "<PERSON><PERSON>", "link", "to", "content", "targeting", "main"], "severity": "info", "metadata": {"scanDuration": 30, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.9, "checkType": "bypass-mechanism-analysis", "skipLinkAnalysis": true, "landmarkAnalysis": true, "navigationAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "timestamp": "2025-07-12T14:13:28.465Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 1, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "warning", "description": "Skip links with invalid targets", "value": "Skip links targeting non-existent elements: login", "selector": "a[href^=\"#\"]:first-child:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["a[href^=\"#\"]:first-child:nth-of-type(2)", "<PERSON><PERSON>", "links", "targeting", "non-existent", "elements", "login"], "severity": "warning", "metadata": {"scanDuration": 30, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.9, "checkType": "bypass-mechanism-analysis", "skipLinkAnalysis": true, "landmarkAnalysis": true, "navigationAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "timestamp": "2025-07-12T14:13:28.465Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 1, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752329608465, "hash": "9d2ac19d32ef3de0c083ed2d6d826efa", "accessCount": 1, "lastAccessed": 1752329608465, "size": 1760, "metadata": {"originalKey": "WCAG-028:WCAG-028", "normalizedKey": "wcag-028_wcag-028", "savedAt": 1752329608466, "version": "1.1", "keyHash": "aa3b9275fe6d7836e86c5b848a3770e7"}}